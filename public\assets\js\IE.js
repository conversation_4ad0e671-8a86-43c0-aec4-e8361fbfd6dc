// eslint-disable-next-line no-unused-vars
function IEVersion() {
  var userAgent = navigator.userAgent // 取得浏览器的userAgent字符串
  var isIE = userAgent.indexOf('compatible') > -1 && userAgent.indexOf('MSIE') > -1 // 判断是否IE<11浏览器
  var isEdge = userAgent.indexOf('Edge') > -1 && !isIE // 判断是否IE的Edge浏览器
  var isIE11 = userAgent.indexOf('Trident') > -1 && userAgent.indexOf('rv:11.0') > -1
  if (isIE) {
    var reIE = new RegExp('MSIE (\\d+\\.\\d+);')
    reIE.test(userAgent)
    var fIEVersion = parseFloat(RegExp['$1'])
    switch (fIEVersion) {
      case 7:
        return 7
      case 8:
        return 8
      case 9:
        return 9
      case 10:
        return 10
      default:
        return 6
    }
  }
  if (isEdge) {
    return 'edge'// edge
  }
  if (isIE11) {
    return 11 // IE11
  }
  return -1// 不是ie浏览器
}
