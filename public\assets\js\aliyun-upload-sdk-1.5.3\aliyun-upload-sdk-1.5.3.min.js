!function(e){function t(r){if(n[r])return n[r].exports;var o=n[r]={i:r,l:!1,exports:{}};return e[r].call(o.exports,o,o.exports,t),o.l=!0,o.exports}var n={};t.m=e,t.c=n,t.d=function(e,n,r){t.o(e,n)||Object.defineProperty(e,n,{configurable:!1,enumerable:!0,get:r})},t.n=function(e){var n=e&&e.__esModule?function(){return e.default}:function(){return e};return t.d(n,"a",n),n},t.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},t.p="",t(t.s=12)}([function(e,t,n){!function(n,r){e.exports=t=r()}(0,function(){var e=e||function(e,t){var n=Object.create||function(){function e(){}return function(t){var n;return e.prototype=t,n=new e,e.prototype=null,n}}(),r={},o=r.lib={},i=o.Base=function(){return{extend:function(e){var t=n(this);return e&&t.mixIn(e),t.hasOwnProperty("init")&&this.init!==t.init||(t.init=function(){t.$super.init.apply(this,arguments)}),t.init.prototype=t,t.$super=this,t},create:function(){var e=this.extend();return e.init.apply(e,arguments),e},init:function(){},mixIn:function(e){for(var t in e)e.hasOwnProperty(t)&&(this[t]=e[t]);e.hasOwnProperty("toString")&&(this.toString=e.toString)},clone:function(){return this.init.prototype.extend(this)}}}(),a=o.WordArray=i.extend({init:function(e,t){e=this.words=e||[],this.sigBytes=void 0!=t?t:4*e.length},toString:function(e){return(e||u).stringify(this)},concat:function(e){var t=this.words,n=e.words,r=this.sigBytes,o=e.sigBytes;if(this.clamp(),r%4)for(var i=0;i<o;i++){var a=n[i>>>2]>>>24-i%4*8&255;t[r+i>>>2]|=a<<24-(r+i)%4*8}else for(var i=0;i<o;i+=4)t[r+i>>>2]=n[i>>>2];return this.sigBytes+=o,this},clamp:function(){var t=this.words,n=this.sigBytes;t[n>>>2]&=4294967295<<32-n%4*8,t.length=e.ceil(n/4)},clone:function(){var e=i.clone.call(this);return e.words=this.words.slice(0),e},random:function(t){for(var n,r=[],o=0;o<t;o+=4){var i=function(t){var t=t,n=987654321,r=4294967295;return function(){n=36969*(65535&n)+(n>>16)&r,t=18e3*(65535&t)+(t>>16)&r;var o=(n<<16)+t&r;return o/=4294967296,(o+=.5)*(e.random()>.5?1:-1)}}(4294967296*(n||e.random()));n=987654071*i(),r.push(4294967296*i()|0)}return new a.init(r,t)}}),s=r.enc={},u=s.Hex={stringify:function(e){for(var t=e.words,n=e.sigBytes,r=[],o=0;o<n;o++){var i=t[o>>>2]>>>24-o%4*8&255;r.push((i>>>4).toString(16)),r.push((15&i).toString(16))}return r.join("")},parse:function(e){for(var t=e.length,n=[],r=0;r<t;r+=2)n[r>>>3]|=parseInt(e.substr(r,2),16)<<24-r%8*4;return new a.init(n,t/2)}},c=s.Latin1={stringify:function(e){for(var t=e.words,n=e.sigBytes,r=[],o=0;o<n;o++){var i=t[o>>>2]>>>24-o%4*8&255;r.push(String.fromCharCode(i))}return r.join("")},parse:function(e){for(var t=e.length,n=[],r=0;r<t;r++)n[r>>>2]|=(255&e.charCodeAt(r))<<24-r%4*8;return new a.init(n,t)}},l=s.Utf8={stringify:function(e){try{return decodeURIComponent(escape(c.stringify(e)))}catch(e){throw new Error("Malformed UTF-8 data")}},parse:function(e){return c.parse(unescape(encodeURIComponent(e)))}},f=o.BufferedBlockAlgorithm=i.extend({reset:function(){this._data=new a.init,this._nDataBytes=0},_append:function(e){"string"==typeof e&&(e=l.parse(e)),this._data.concat(e),this._nDataBytes+=e.sigBytes},_process:function(t){var n=this._data,r=n.words,o=n.sigBytes,i=this.blockSize,s=4*i,u=o/s;u=t?e.ceil(u):e.max((0|u)-this._minBufferSize,0);var c=u*i,l=e.min(4*c,o);if(c){for(var f=0;f<c;f+=i)this._doProcessBlock(r,f);var h=r.splice(0,c);n.sigBytes-=l}return new a.init(h,l)},clone:function(){var e=i.clone.call(this);return e._data=this._data.clone(),e},_minBufferSize:0}),h=(o.Hasher=f.extend({cfg:i.extend(),init:function(e){this.cfg=this.cfg.extend(e),this.reset()},reset:function(){f.reset.call(this),this._doReset()},update:function(e){return this._append(e),this._process(),this},finalize:function(e){return e&&this._append(e),this._doFinalize()},blockSize:16,_createHelper:function(e){return function(t,n){return new e.init(n).finalize(t)}},_createHmacHelper:function(e){return function(t,n){return new h.HMAC.init(e,n).finalize(t)}}}),r.algo={});return r}(Math);return e})},function(e,t,n){"use strict";function r(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}Object.defineProperty(t,"__esModule",{value:!0});var o=function(){function e(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(t,n,r){return n&&e(t.prototype,n),r&&e(t,r),t}}(),i=function(){function e(){r(this,e)}return o(e,null,[{key:"detectIEVersion",value:function(){for(var e=4,t=document.createElement("div"),n=t.getElementsByTagName("i");t.innerHTML="\x3c!--[if gt IE "+e+"]><i></i><![endif]--\x3e",n[0];)e++;return e>4&&e}},{key:"extend",value:function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&t[n]&&(e[n]=t[n])}},{key:"isArray",value:function(e){return"[object Array]"===Object.prototype.toString.call(arg)}},{key:"getFileType",value:function(e){return e=e.toLowerCase(),/.mp4|.flv|.m3u8|.avi|.rm|.rmvb|.mpeg|.mpg|.mov|.wmv|.3gp|.asf|.dat|.dv|.f4v|.gif|.m2t|.m4v|.mj2|.mjpeg|.mpe|.mts|.ogg|.qt|.swf|.ts|.vob|.wmv|.webm/.test(e)?"video":/.mp3|.wav|.ape|.cda|.au|.midi|.mac|.aac|.ac3|.acm|.amr|.caf|.flac|.m4a|.ra|.wma/.test(e)?"audio":/.bmp|.jpg|.jpeg|.png/.test(e)?"img":"other"}},{key:"isImage",value:function(e){return e=e.toLowerCase(),!!/.jpg|.jpeg|.png/.test(e)}},{key:"ISODateString",value:function(e){function t(e){return e<10?"0"+e:e}return e.getUTCFullYear()+"-"+t(e.getUTCMonth()+1)+"-"+t(e.getUTCDate())+"T"+t(e.getUTCHours())+":"+t(e.getUTCMinutes())+":"+t(e.getUTCSeconds())+"Z"}},{key:"isIntNum",value:function(e){return!!/^\d+$/.test(e)}}]),e}();t.default=i},function(e,t,n){!function(r,o){e.exports=t=o(n(0))}(0,function(e){return function(t){function n(e,t,n,r,o,i,a){var s=e+(t&n|~t&r)+o+a;return(s<<i|s>>>32-i)+t}function r(e,t,n,r,o,i,a){var s=e+(t&r|n&~r)+o+a;return(s<<i|s>>>32-i)+t}function o(e,t,n,r,o,i,a){var s=e+(t^n^r)+o+a;return(s<<i|s>>>32-i)+t}function i(e,t,n,r,o,i,a){var s=e+(n^(t|~r))+o+a;return(s<<i|s>>>32-i)+t}var a=e,s=a.lib,u=s.WordArray,c=s.Hasher,l=a.algo,f=[];!function(){for(var e=0;e<64;e++)f[e]=4294967296*t.abs(t.sin(e+1))|0}();var h=l.MD5=c.extend({_doReset:function(){this._hash=new u.init([1732584193,4023233417,2562383102,271733878])},_doProcessBlock:function(e,t){for(var a=0;a<16;a++){var s=t+a,u=e[s];e[s]=16711935&(u<<8|u>>>24)|4278255360&(u<<24|u>>>8)}var c=this._hash.words,l=e[t+0],h=e[t+1],d=e[t+2],p=e[t+3],g=e[t+4],v=e[t+5],y=e[t+6],_=e[t+7],m=e[t+8],T=e[t+9],S=e[t+10],w=e[t+11],A=e[t+12],b=e[t+13],I=e[t+14],E=e[t+15],k=c[0],U=c[1],P=c[2],O=c[3];k=n(k,U,P,O,l,7,f[0]),O=n(O,k,U,P,h,12,f[1]),P=n(P,O,k,U,d,17,f[2]),U=n(U,P,O,k,p,22,f[3]),k=n(k,U,P,O,g,7,f[4]),O=n(O,k,U,P,v,12,f[5]),P=n(P,O,k,U,y,17,f[6]),U=n(U,P,O,k,_,22,f[7]),k=n(k,U,P,O,m,7,f[8]),O=n(O,k,U,P,T,12,f[9]),P=n(P,O,k,U,S,17,f[10]),U=n(U,P,O,k,w,22,f[11]),k=n(k,U,P,O,A,7,f[12]),O=n(O,k,U,P,b,12,f[13]),P=n(P,O,k,U,I,17,f[14]),U=n(U,P,O,k,E,22,f[15]),k=r(k,U,P,O,h,5,f[16]),O=r(O,k,U,P,y,9,f[17]),P=r(P,O,k,U,w,14,f[18]),U=r(U,P,O,k,l,20,f[19]),k=r(k,U,P,O,v,5,f[20]),O=r(O,k,U,P,S,9,f[21]),P=r(P,O,k,U,E,14,f[22]),U=r(U,P,O,k,g,20,f[23]),k=r(k,U,P,O,T,5,f[24]),O=r(O,k,U,P,I,9,f[25]),P=r(P,O,k,U,p,14,f[26]),U=r(U,P,O,k,m,20,f[27]),k=r(k,U,P,O,b,5,f[28]),O=r(O,k,U,P,d,9,f[29]),P=r(P,O,k,U,_,14,f[30]),U=r(U,P,O,k,A,20,f[31]),k=o(k,U,P,O,v,4,f[32]),O=o(O,k,U,P,m,11,f[33]),P=o(P,O,k,U,w,16,f[34]),U=o(U,P,O,k,I,23,f[35]),k=o(k,U,P,O,h,4,f[36]),O=o(O,k,U,P,g,11,f[37]),P=o(P,O,k,U,_,16,f[38]),U=o(U,P,O,k,S,23,f[39]),k=o(k,U,P,O,b,4,f[40]),O=o(O,k,U,P,l,11,f[41]),P=o(P,O,k,U,p,16,f[42]),U=o(U,P,O,k,y,23,f[43]),k=o(k,U,P,O,T,4,f[44]),O=o(O,k,U,P,A,11,f[45]),P=o(P,O,k,U,E,16,f[46]),U=o(U,P,O,k,d,23,f[47]),k=i(k,U,P,O,l,6,f[48]),O=i(O,k,U,P,_,10,f[49]),P=i(P,O,k,U,I,15,f[50]),U=i(U,P,O,k,v,21,f[51]),k=i(k,U,P,O,A,6,f[52]),O=i(O,k,U,P,p,10,f[53]),P=i(P,O,k,U,S,15,f[54]),U=i(U,P,O,k,h,21,f[55]),k=i(k,U,P,O,m,6,f[56]),O=i(O,k,U,P,E,10,f[57]),P=i(P,O,k,U,y,15,f[58]),U=i(U,P,O,k,b,21,f[59]),k=i(k,U,P,O,g,6,f[60]),O=i(O,k,U,P,w,10,f[61]),P=i(P,O,k,U,d,15,f[62]),U=i(U,P,O,k,T,21,f[63]),c[0]=c[0]+k|0,c[1]=c[1]+U|0,c[2]=c[2]+P|0,c[3]=c[3]+O|0},_doFinalize:function(){var e=this._data,n=e.words,r=8*this._nDataBytes,o=8*e.sigBytes;n[o>>>5]|=128<<24-o%32;var i=t.floor(r/4294967296),a=r;n[15+(o+64>>>9<<4)]=16711935&(i<<8|i>>>24)|4278255360&(i<<24|i>>>8),n[14+(o+64>>>9<<4)]=16711935&(a<<8|a>>>24)|4278255360&(a<<24|a>>>8),e.sigBytes=4*(n.length+1),this._process();for(var s=this._hash,u=s.words,c=0;c<4;c++){var l=u[c];u[c]=16711935&(l<<8|l>>>24)|4278255360&(l<<24|l>>>8)}return s},clone:function(){var e=c.clone.call(this);return e._hash=this._hash.clone(),e}});a.MD5=c._createHelper(h),a.HmacMD5=c._createHmacHelper(h)}(Math),e.MD5})},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});t.UPLOADSTATE={INIT:"init",UPLOADING:"uploading",COMPLETE:"complete",INTERRUPT:"interrupt"},t.UPLOADSTEP={INIT:"init",PART:"part",COMPLETE:"complete"},t.UPLOADDEFAULT={PARALLEL:5,PARTSIZE:1048576}},function(e,t,n){"use strict";function r(e){return e&&e.__esModule?e:{default:e}}function o(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}Object.defineProperty(t,"__esModule",{value:!0});var i=function(){function e(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(t,n,r){return n&&e(t.prototype,n),r&&e(t,r),t}}(),a=n(5),s=r(a),u=n(25),c=r(u),l=n(6),f=r(l),h=n(7),d=r(h),p=function(){function e(t){o(this,e);var n=d.default.os.name,r=d.default.os.version||"",i=d.default.browser.name,a=d.default.browser.version||"",u=window.location.href,c="";u&&(c=d.default.getHost(u));var l="pc";d.default.os.ipad?l="pad":(d.default.os.iphone||d.default.os.android)&&(l="phone"),this._ri=s.default.create(),this.initParam={APIVersion:"0.6.0",lv:"1",av:f.default.version,pd:"upload",sm:"upload",md:"uploader",uuid:e.getUuid(),os:n,ov:r,et:i,ev:a,uat:navigator.userAgent,app_n:c,tt:l,dm:"h5",ut:""}}return i(e,[{key:"log",value:function(e,t){t&&t.ri?(this._ri=t.ri,delete t.ri):this._ri=s.default.create(),t&&t.ut&&(this.initParam.ut=t.ut,delete t.ut),this.initParam.t=(new Date).getTime(),this.initParam.ll="20006"==e?"error":"info",this.initParam.ri=this._ri,this.initParam.e=e;var n=[];if(t)for(var r in t)n.push(r+"="+t[r]);var o=n.join("&");this.initParam.args=encodeURIComponent(""==o?"0":o);var i=[];for(var r in this.initParam)i.push(r+"="+this.initParam[r]);var a=i.join("&");if(AliyunUpload&&AliyunUpload.__logTestCallback__)AliyunUpload.__logTestCallback__(a);else{new Image(0,0).src="https://videocloud.cn-hangzhou.log.aliyuncs.com/logstores/upload/track?"+a}}}],[{key:"getUuid",value:function(){var e=c.default.get("p_h5_upload_u");return e||(e=s.default.create(),c.default.set("p_h5_upload_u",e,730)),e}},{key:"getClientId",value:function(){return c.default.get("p_h5_upload_clientId")}},{key:"setClientId",value:function(e){return e||(e=s.default.create()),c.default.set("p_h5_upload_clientId",e,730),e}}]),e}();t.default=p},function(e,t,n){"use strict";function r(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}Object.defineProperty(t,"__esModule",{value:!0});var o=function(){function e(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(t,n,r){return n&&e(t.prototype,n),r&&e(t,r),t}}(),i=function(){function e(){r(this,e)}return o(e,null,[{key:"create",value:function(e,t){var n,r="0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz".split(""),o=[];if(t=t||r.length,e)for(n=0;n<e;n++)o[n]=r[0|Math.random()*t];else{var i;for(o[8]=o[13]=o[18]=o[23]="-",o[14]="4",n=0;n<36;n++)o[n]||(i=0|16*Math.random(),o[n]=r[19==n?3&i|8:i])}return o.join("")}}]),e}();t.default=i},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r={version:"1.5.0"};t.default=r},function(e,t,n){"use strict";function r(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}Object.defineProperty(t,"__esModule",{value:!0});var o=function(){function e(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(t,n,r){return n&&e(t.prototype,n),r&&e(t,r),t}}(),i=function(e){var t=navigator.userAgent,n="other";if(e.ios)return"iOS";if(e.android)return"android";if(t.indexOf("Baiduspider")>-1)return"Baiduspider";if(t.indexOf("PlayStation")>-1)return"PS4";var r="Win32"==navigator.platform||"Windows"==navigator.platform||t.indexOf("Windows")>-1,o="Mac68K"==navigator.platform||"MacPPC"==navigator.platform||"Macintosh"==navigator.platform||"MacIntel"==navigator.platform;return o&&(n="macOS"),"X11"==navigator.platform&&!r&&!o&&(n="Unix"),String(navigator.platform).indexOf("Linux")>-1&&(n="Linux"),r?"windows":n},a=function(){var e=navigator.userAgent,t="";return(e.indexOf("Windows NT 5.0")>-1||e.indexOf("Windows 2000")>-1)&&(t="2000"),(e.indexOf("Windows NT 5.1")>-1||e.indexOf("Windows XP")>-1)&&(t="XP"),(e.indexOf("Windows NT 5.2")>-1||e.indexOf("Windows 2003")>-1)&&(t="2003"),(e.indexOf("Windows NT 6.0")>-1||e.indexOf("Windows Vista")>-1)&&(t="Vista"),(e.indexOf("Windows NT 6.1")>-1||e.indexOf("Windows 7")>-1)&&(t="7"),(e.indexOf("Windows NT 6.2")>-1||e.indexOf("Windows 8")>-1)&&(t="8"),(e.indexOf("Windows NT 6.3")>-1||e.indexOf("Windows 8.1")>-1)&&(t="8.1"),(e.indexOf("Windows NT 10")>-1||e.indexOf("Windows 10")>-1)&&(t="10"),t},s=function(e){var t=navigator.userAgent.toLowerCase();return e.chrome?"Chrome":e.firefox?"Firefox":e.safari?"Safari":e.webview?"webview":e.ie?/edge/.test(t)?"Edge":"IE":/baiduspider/.test(t)?"Baiduspider":/ucweb/.test(t)||/UCBrowser/.test(t)?"UC":/opera/.test(t)?"Opera":/ucweb/.test(t)?"UC":/360se/.test(t)?"360浏览器":/bidubrowser/.test(t)?"百度浏览器":/metasr/.test(t)?"搜狗浏览器":/lbbrowser/.test(t)?"猎豹浏览器":/micromessenger/.test(t)?"微信内置浏览器":/qqbrowser/.test(t)?"QQ浏览器":/playstation/.test(t)?"PS4浏览器":void 0},u=function(){var e={},t={},n=navigator.userAgent,r=navigator.platform,o=n.match(/Web[kK]it[\/]{0,1}([\d.]+)/),u=n.match(/(Android);?[\s\/]+([\d.]+)?/),c=!!n.match(/\(Macintosh\; Intel /),l=n.match(/(iPad).*OS\s([\d_]+)/),f=n.match(/(iPod)(.*OS\s([\d_]+))?/),h=!l&&n.match(/(iPhone\sOS)\s([\d_]+)/),d=n.match(/(webOS|hpwOS)[\s\/]([\d.]+)/),p=/Win\d{2}|Windows/.test(r),g=n.match(/Windows Phone ([\d.]+)/),v=d&&n.match(/TouchPad/),y=n.match(/Kindle\/([\d.]+)/),_=n.match(/Silk\/([\d._]+)/),m=n.match(/(BlackBerry).*Version\/([\d.]+)/),T=n.match(/(BB10).*Version\/([\d.]+)/),S=n.match(/(RIM\sTablet\sOS)\s([\d.]+)/),w=n.match(/PlayBook/),A=n.match(/Chrome\/([\d.]+)/)||n.match(/CriOS\/([\d.]+)/),b=n.match(/Firefox\/([\d.]+)/),I=n.match(/\((?:Mobile|Tablet); rv:([\d.]+)\).*Firefox\/[\d.]+/),E=n.match(/MSIE\s([\d.]+)/)||n.match(/Trident\/[\d](?=[^\?]+).*rv:([0-9.].)/),k=!A&&n.match(/(iPhone|iPod|iPad).*AppleWebKit(?!.*Safari)/),U=k||n.match(/Version\/([\d.]+)([^S](Safari)|[^M]*(Mobile)[^S]*(Safari))/);if((t.webkit=!!o)&&(t.version=o[1]),u&&(e.android=!0,e.version=u[2]),h&&!f&&(e.ios=e.iphone=!0,e.version=h[2].replace(/_/g,".")),l&&(e.ios=e.ipad=!0,e.version=l[2].replace(/_/g,".")),f&&(e.ios=e.ipod=!0,e.version=f[3]?f[3].replace(/_/g,"."):null),g&&(e.wp=!0,e.version=g[1]),d&&(e.webos=!0,e.version=d[2]),v&&(e.touchpad=!0),m&&(e.blackberry=!0,e.version=m[2]),T&&(e.bb10=!0,e.version=T[2]),S&&(e.rimtabletos=!0,e.version=S[2]),w&&(t.playbook=!0),y&&(e.kindle=!0,e.version=y[1]),_&&(t.silk=!0,t.version=_[1]),!_&&e.android&&n.match(/Kindle Fire/)&&(t.silk=!0),A&&(t.chrome=!0,t.version=A[1]),b&&(t.firefox=!0,t.version=b[1]),I&&(e.firefoxos=!0,e.version=I[1]),E&&(t.ie=!0,t.version=E[1]),U&&(c||e.ios||p||u)&&(t.safari=!0,e.ios||(t.version=U[1])),k&&(t.webview=!0),c){var P=n.match(/[\d]*_[\d]*_[\d]*/);P&&P.length>0&&P[0]&&(e.version=P[0].replace(/_/g,"."))}return e.tablet=!!(l||w||u&&!n.match(/Mobile/)||b&&n.match(/Tablet/)||E&&!n.match(/Phone/)&&n.match(/Touch/)),e.phone=!(e.tablet||e.ipod||!(u||h||d||m||T||A&&n.match(/Android/)||A&&n.match(/CriOS\/([\d.]+)/)||b&&n.match(/Mobile/)||E&&n.match(/Touch/))),e.pc=!e.tablet&&!e.phone,c?e.name="macOS":p?(e.name="windows",e.version=a()):e.name=i(e),t.name=s(t),{os:e,browser:t}}(),c=function(){function e(){r(this,e)}return o(e,null,[{key:"getHost",value:function(e){var t="";if(void 0===e||null==e||""==e)return"";var n=e.indexOf("//"),r=e;n>-1&&(r=e.substring(n+2));var t=r,o=r.split("/");return o&&o.length>0&&(t=o[0]),o=t.split(":"),o&&o.length>0&&(t=o[0]),t}},{key:"os",get:function(){return u.os}},{key:"browser",get:function(){var e=u.browser;return e.name||(e.name=s()),e}}]),e}();t.default=c},function(e,t,n){"use strict";function r(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}Object.defineProperty(t,"__esModule",{value:!0});var o=function(){function e(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(t,n,r){return n&&e(t.prototype,n),r&&e(t,r),t}}(),i=function(){function e(){r(this,e)}return o(e,null,[{key:"get",value:function(e,t,n,r,o){var i;n=n||function(){},"undefined"==typeof XMLHttpRequest&&(window.XMLHttpRequest=function(){try{return new window.ActiveXObject("Msxml2.XMLHTTP.6.0")}catch(e){}try{return new window.ActiveXObject("Msxml2.XMLHTTP.3.0")}catch(e){}try{return new window.ActiveXObject("Msxml2.XMLHTTP")}catch(e){}throw new Error("This browser does not support XMLHttpRequest.")}),i=new XMLHttpRequest,i.onreadystatechange=function(){4===i.readyState&&(200===i.status?t(i.responseText):n(i.responseText))};try{void 0===r&&(r=!0),i.open("GET",e,r),o&&(i.withCredentials=!0)}catch(e){return void n(e)}try{i.send()}catch(e){n(e)}}}]),e}();t.default=i},function(e,t,n){"use strict";function r(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}Object.defineProperty(t,"__esModule",{value:!0});var o=function(){function e(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(t,n,r){return n&&e(t.prototype,n),r&&e(t,r),t}}(),i=n(27),a=n(30),s=(n(10),function(){function e(){r(this,e)}return o(e,null,[{key:"randomUUID",value:function(){for(var e=[],t="0123456789abcdef",n=0;n<36;n++)e[n]=t.substr(Math.floor(16*Math.random()),1);return e[14]="4",e[19]=t.substr(3&e[19]|8,1),e[8]=e[13]=e[18]=e[23]="-",e.join("")}},{key:"aliyunEncodeURI",value:function(e){var t=encodeURIComponent(e);return t=t.replace(/\+/g,"%20").replace(/\*/g,"%2A").replace(/%7E/g,"~").replace(/!/g,"%21").replace(/\(/g,"%28").replace(/\)/g,"%29").replace(/'/g,"%27")}},{key:"makeUTF8sort",value:function(t,n,r){if(!t)throw new Error("PrismPlayer Error: vid should not be null!");var o=[];for(var i in t)o.push(i);for(var a=o.sort(),s="",u=a.length,i=0;i<u;i++){var c=e.aliyunEncodeURI(a[i]),l=e.aliyunEncodeURI(t[a[i]]);""==s?s=c+n+l:s+=r+c+n+l}return s}},{key:"makeChangeSiga",value:function(t,n){if(!t)throw new Error("PrismPlayer Error: vid should not be null!");return a.stringify(i("GET&"+e.aliyunEncodeURI("/")+"&"+e.aliyunEncodeURI(e.makeUTF8sort(t,"=","&")),n+"&"))}}]),e}());t.default=s},function(e,t,n){!function(r,o){e.exports=t=o(n(0))}(0,function(e){return e.enc.Utf8})},function(e,t,n){!function(r,o){e.exports=t=o(n(0))}(0,function(e){return e.enc.Hex})},function(e,t,n){e.exports=n(13)},function(e,t,n){"use strict";var r,o,i=("function"==typeof Symbol&&Symbol.iterator,n(14)),a=function(e){return e&&e.__esModule?e:{default:e}}(i),s={Vod:a.default};r=[],void 0!==(o=function(){return s}.apply(t,r))&&(e.exports=o),FileReader.prototype.readAsBinaryString||(FileReader.prototype.readAsBinaryString=function(e){var t="",n=this,r=new FileReader;r.onload=function(e){for(var o=new Uint8Array(r.result),i=o.byteLength,a=0;a<i;a++)t+=String.fromCharCode(o[a]);n.content=t,n.onload()},r.readAsArrayBuffer(e)}),window.AliyunUpload=s},function(e,t,n){"use strict";function r(e){return e&&e.__esModule?e:{default:e}}function o(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}Object.defineProperty(t,"__esModule",{value:!0});var i=function(){function e(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(t,n,r){return n&&e(t.prototype,n),r&&e(t,r),t}}(),a=n(15),s=n(3),u=n(16),c=r(u),l=n(18),f=r(l),h=n(24),d=r(h),p=n(4),g=r(p),v=n(1),y=r(v),_=n(5),m=r(_),T=n(26),S=r(T),w=n(31),A=r(w),b=n(32),I=r(b),E=(n(2),function(){function e(t){o(this,e),this.options=t,this.options.partSize=this.options.partSize||s.UPLOADDEFAULT.PARTSIZE,this.options.parallel=this.options.parallel||s.UPLOADDEFAULT.PARALLEL,this.options.region=this.options.region||"cn-shanghai",this.options.cname=this.options.cname||!1,this.options.localCheckpoint=this.options.localCheckpoint||!1,this.options.enableUploadProgress=this.options.enableUploadProgress||!0,this._ossCreditor=new Object,this._state=a.VODSTATE.INIT,this._uploadList=[],this._curIndex=-1,this._ossUpload=null,this._log=new g.default,this._retryCount=0,this._retryTotal=this.options.retryCount||3,this._retryDuration=this.options.retryDuration||2,this._state=a.VODSTATE.INIT,this._uploadWay="vod",this._onbeforeunload=!1,this._invalidUserId=!1,this._initEvent()}return i(e,[{key:"init",value:function(e,t,n,r){return this._retryCount=0,!(n&&!r||!n&&r)&&(!(e&&!t||!e&&t)&&(this._ossCreditor.accessKeyId=e,this._ossCreditor.accessKeySecret=t,this._ossCreditor.securityToken=n,this._ossCreditor.expireTime=r,!0))}},{key:"addFile",value:function(e,t,n,r,o,i){if(!e)return!1;if(0==e.size)try{this.options.onUploadFailed({file:e},"EmptyFile","文件大小为0，不能上传")}catch(e){console.log(e)}for(var s=(this.options,0);s<this._uploadList.length;s++)if(this._uploadList[s].file==e)return!1;var u=new Object;if(u.file=e,u._endpoint=t,u._bucket=n,u._object=r,u.state=a.UPLOADSTATE.INIT,u.isImage=y.default.isImage(e.name),o&&(u.videoInfo=o?JSON.parse(o).Vod:{},u.userData=f.default.encode(o)),u.ri=m.default.create(),this._uploadList.push(u),!u.isImage&&this.options.enableUploadProgress){var c=this;I.default.getMd5(e,function(e){u.fileHash=e;var t=c._getCheckoutpoint(u);c.options.localCheckpoint||t?c.addFileCallback(u):c._getCheckoutpointFromCloud(u,function(e){if(e.UploadPoint){var t=JSON.parse(e.UploadPoint);1!=t.loaded&&(u.checkpoint=t.checkpoint,u.loaded=t.loaded,u.videoId=e.VideoId,c._saveCheckoutpoint(u,t.checkpoint))}c.addFileCallback(u)},function(e){try{if((e=JSON.parse(e))&&"InvalidParameter"==e.Code&&e.Message.indexOf("UserId")>0){c._invalidUserId=!0;var t=e.Message+"，正确账号ID(userId)请参考：https://help.aliyun.com/knowledge_detail/37196.html";console.log(t)}}catch(e){console.log(e)}})})}else this.addFileCallback(u)}},{key:"addFileCallback",value:function(e){this._reportLog("20001",e,{ql:this._uploadList.length});try{this.options.addFileSuccess&&this.options.addFileSuccess(e)}catch(e){console.log(e)}return!0}},{key:"deleteFile",value:function(e){return!!this.cancelFile(e)&&(this._uploadList.splice(e,1),!0)}},{key:"cleanList",value:function(){this.stopUpload(),this._uploadList.length=0,this._curIndex=-1}},{key:"cancelFile",value:function(e){this.options;if(e<0||e>=this._uploadList.length)return!1;var t=this._uploadList[e];if(e==this._curIndex&&t.state==a.UPLOADSTATE.UPLOADING){t.state=a.UPLOADSTATE.CANCELED;var n=this._getCheckoutpoint(t);n&&n.checkpoint&&(n=n.checkpoint),n&&this._ossUpload.cancel(),this._removeCheckoutpoint(t),this.nextUpload()}else t.state!=a.UPLOADSTATE.SUCCESS&&(t.state=a.UPLOADSTATE.CANCELED);return this._reportLog("20008",t),!0}},{key:"resumeFile",value:function(e){this.options;if(e<0||e>=this._uploadList.length)return!1;var t=this._uploadList[e];return t.state==a.UPLOADSTATE.CANCELED&&(t.state=a.UPLOADSTATE.INIT,!0)}},{key:"listFiles",value:function(){return this._uploadList}},{key:"getCheckpoint",value:function(e){return this._getCheckoutpoint({file:e})}},{key:"startUpload",value:function(e){this._retryCount=0;this.options;if(this._state==a.VODSTATE.START||this._state==a.VODSTATE.EXPIRE)return void console.log("already started or expired");if(this._initState(),this._curIndex=this._findUploadIndex(),-1==this._curIndex)return void(this._state=a.VODSTATE.END);var t=this._uploadList[this._curIndex];this._ossUpload=null,this._upload(t),this._state=a.VODSTATE.START}},{key:"nextUpload",value:function(){var e=this.options;if(this._state==a.VODSTATE.START)if(this._curIndex=this._findUploadIndex(),-1!=this._curIndex){var t=this._uploadList[this._curIndex];this._ossUpload=null,this._upload(t)}else{this._state=a.VODSTATE.END;try{e.onUploadEnd&&e.onUploadEnd(t)}catch(e){console.log(e)}}}},{key:"clear",value:function(e){for(var t=this.options,n=0,r=0;r<this._uploadList.length;r++)t.uploadList[r].state==a.UPLOADSTATE.SUCCESS&&n++,this._uploadList[r].state==e&&(t.uploadList.splice(r,1),r--);t.onClear&&t.onClear(t.uploadList.length,n)}},{key:"stopUpload",value:function(){if((this._state==a.VODSTATE.START||this._state==a.VODSTATE.FAILURE||-1==this._curIndex)&&-1!=this._curIndex){var e=this._uploadList[this._curIndex];this._state=a.VODSTATE.STOP,e.state=a.UPLOADSTATE.STOPED,this._changeState(e,a.UPLOADSTATE.STOPED),this._ossUpload.cancel()}}},{key:"resumeUploadWithAuth",value:function(e){var t=this;if(!e)return!1;var n=JSON.parse(f.default.decode(e));return!!(n.AccessKeyId&&n.AccessKeySecret&&n.SecurityToken&&n.Expiration)&&t.resumeUploadWithToken(n.AccessKeyId,n.AccessKeySecret,n.SecurityToken,n.Expiration)}},{key:"resumeUploadWithToken",value:function(e,t,n,r){this.options;if(!(e&&t&&n&&r))return!1;if(this._state!=a.VODSTATE.EXPIRE)return!1;if(-1==this._curIndex)return!1;var o="";return this._uploadList.length>this._curIndex&&(o=this._uploadList[this._curIndex]),o&&(this.init(e,t,n,r),this._state=a.VODSTATE.START,this._ossUpload=null,this._uploadCore(o,o.retry),o.retry=!1),!0}},{key:"resumeUploadWithSTSToken",value:function(e,t,n){if(-1==this._curIndex)return!1;if(this._state!=a.VODSTATE.EXPIRE)return!1;if(this._uploadList.length>this._curIndex){var r=this._uploadList[this._curIndex];r.object?this._refreshSTSTokenUpload(r,e,t,n):this.setSTSToken(r,e,t,n)}}},{key:"setSTSTokenDirectlyUpload",value:function(e,t,n,r,o){if(!(t&&n&&r&&o))return console.log("accessKeyId、ccessKeySecret、securityToken and expiration should not be empty."),!1;this._ut="oss";var i=e;this.init(t,n,r,o),i.endpoint=i._endpoint,i.bucket=i._bucket,i.object=i._object,this._ossUpload=null,this._uploadCore(i,e.retry),e.retry=!1}},{key:"setSTSToken",value:function(e,t,n,r){if(!t||!n||!r)return console.log("accessKeyId、ccessKeySecret、securityToken should not be empty."),!1;this._ut="vod",this._uploadWay="sts";var o=e.videoInfo,i={accessKeyId:t,securityToken:r,accessKeySecret:n,fileName:e.file.name,title:o.Title,requestId:e.ri,region:this.options.region};o.ImageType&&(i.imageType=o.ImageType),o.ImageExt&&(i.imageExt=o.ImageExt),o.FileSize&&(i.fileSize=o.FileSize),o.Description&&(i.description=o.Description),o.CateId&&(i.cateId=o.CateId),o.Tags&&(i.tags=o.Tags),o.TemplateGroupId&&(i.templateGroupId=o.TemplateGroupId),o.StorageLocation&&(i.storageLocation=o.StorageLocation),o.CoverURL&&(i.coverUrl=o.CoverURL),o.TransCodeMode&&(i.transCodeMode=o.TransCodeMode),o.UserData&&(i.userData=o.UserData),o.WorkflowId&&(i.WorkflowId=o.WorkflowId),o.AppId&&(i.AppId=o.AppId);var s=this,u="getUploadAuth";e.videoId?(i.videoId=e.videoId,u="refreshUploadAuth"):e.isImage&&(u="getImageUploadAuth"),S.default[u](i,function(t){e.videoId=t.VideoId?t.VideoId:e.videoId,s.setUploadAuthAndAddress(e,t.UploadAuth,t.UploadAddress),s._state=a.VODSTATE.START},function(t){s._error(e,{name:t.Code,code:t.Code,message:t.Message,requestId:t.RequestId})})}},{key:"setUploadAuthAndAddress",value:function(e,t,n,r){if(!e||!t||!n)return!1;var o=JSON.parse(f.default.decode(t));if(!(o.AccessKeyId&&o.AccessKeySecret&&o.SecurityToken&&o.Expiration))return console.error("uploadauth is invalid"),!1;var i={},a=e;if(n){if(i=JSON.parse(f.default.decode(n)),!i.Endpoint||!i.Bucket||!i.FileName)return console.error("uploadAddress is invalid"),!1}else i.Endpoint=a.endpoint,i.Bucket=a.bucket,i.FileName=a.object;this._ut="vod",this._uploadWay="vod",this.options.region=o.Region||this.options.region,this.init(o.AccessKeyId,o.AccessKeySecret,o.SecurityToken,o.Expiration),a.endpoint=a._endpoint?a._endpoint:i.Endpoint,a.bucket=a._bucket?a._bucket:i.Bucket,a.object=a._object?a._object:i.FileName,a.region=this.options.region,r&&(a.videoId=r),this._ossUpload=null,this._uploadCore(a,e.retry),e.retry=!1}},{key:"_refreshSTSTokenUpload",value:function(e,t,n,r){if(!t||!n||!r)return console.log("accessKeyId、ccessKeySecret、securityToken should not be empty."),!1;var o={accessKeyId:t,securityToken:r,accessKeySecret:n,videoId:e.object,requestId:e.ri,region:this.options.region},i=this,s="refreshUploadAuth";e.isImage&&(s="getImageUploadAuth"),S.default[s](o,function(t){i.setUploadAuthAndAddress(e,t.UploadAuth,UploadAddress),i._state=a.VODSTATE.START},function(t){i._error(e,{name:t.Code,code:t.Code,message:t.Message,requestId:t.RequestId})})}},{key:"_upload",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n=this.options;if(e.retry=t,n.onUploadstarted&&!t)try{var r=this._getCheckoutpoint(e);r&&r.state!=a.UPLOADSTATE.UPLOADING&&(e.checkpoint=r,e.videoId=r.videoId),n.onUploadstarted(e)}catch(e){console.log(e)}}},{key:"_uploadCore",value:function(e){arguments.length>1&&void 0!==arguments[1]&&arguments[1];if(!this._ossCreditor.accessKeyId||!this._ossCreditor.accessKeySecret||!this._ossCreditor.securityToken)throw new Error("AccessKeyId、AccessKeySecret、securityToken should not be null");if(e.state=a.UPLOADSTATE.UPLOADING,!this._ossUpload){e.endpoint=e.endpoint||"http://oss-cn-hangzhou.aliyuncs.com";var t=this;this._ossUpload=new c.default({bucket:e.bucket,endpoint:e.endpoint,AccessKeyId:this._ossCreditor.accessKeyId,AccessKeySecret:this._ossCreditor.accessKeySecret,SecurityToken:this._ossCreditor.securityToken,timeout:this.options.timeout,cname:this.options.cname},{onerror:function(e,n){t._error.call(t,e,n)},oncomplete:function(e,n){t._complete.call(t,e,n)},onprogress:function(e,n,r){t._progress.call(t,e,n,r)}})}var n=y.default.getFileType(e.file.name),r=this._getCheckoutpoint(e),o="",i="";r&&r.checkpoint&&(i=r.state,o=r.videoId,r=r.checkpoint),r&&o==e.videoId&&i!=a.UPLOADSTATE.UPLOADING&&(r.file=e.file,e.checkpoint=r,r.uploadId);var s=this._adjustPartSize(e);this._reportLog("20002",e,{ft:n,fs:e.file.size,bu:e.bucket,ok:e.object,vid:e.videoId||"",fn:e.file.name,fw:null,fh:null,ps:s});var u={headers:{"x-oss-notification":e.userData?e.userData:""},partSize:s,parallel:this.options.parallel};this._ossUpload.upload(e,u)}},{key:"_findUploadIndex",value:function(){for(var e=-1,t=0;t<this._uploadList.length;t++)if(this._uploadList[t].state==a.UPLOADSTATE.INIT){e=t;break}return e}},{key:"_error",value:function(e,t){if("cancel"==t.name)try{this.options.onUploadCanceled(e,t)}catch(e){console.log(e)}else{if(t.message.indexOf("InvalidAccessKeyIdError")>0||"SignatureDoesNotMatchError"==t.name||"SecurityTokenExpired"==t.code||"InvalidSecurityToken.Expired"==t.code||"InvalidAccessKeyId"==t.code&&this._ossCreditor.securityToken){if(this.options.onUploadTokenExpired){this._state=a.VODSTATE.EXPIRE,e.state=a.UPLOADSTATE.FAIlURE;try{this.options.onUploadTokenExpired(e,t)}catch(e){console.log(e)}}return}if(("RequestTimeoutError"==t.name||"ConnectionTimeout"==t.name||"ConnectionTimeoutError"==t.name)&&this._retryTotal>this._retryCount){var n=this;return setTimeout(function(){n._uploadCore(e,!0)},1e3*n._retryDuration),void this._retryCount++}"NoSuchUploadError"==t.name&&this._removeCheckoutpoint(e),this._handleError(e,t)}}},{key:"_handleError",value:function(e,t){var n=!(arguments.length>2&&void 0!==arguments[2])||arguments[2],r=a.UPLOADSTATE.FAIlURE;if(e.state!=a.UPLOADSTATE.CANCELED&&(e.state=a.UPLOADSTATE.FAIlURE,this._state=a.VODSTATE.FAILURE,this.options.onUploadFailed&&t&&t.code&&t.message))try{this.options.onUploadFailed(e,t.code,t.message)}catch(e){console.log(e)}if(n&&this._changeState(e,r),this._reportLog("20006",e,{code:t.name,message:t.message,requestId:t.requestId,fs:e.file.size,bu:e.bucket,ok:e.object,fn:e.file.name}),this._reportLog("20004",e,{requestId:t.requestId,fs:e.file.size,bu:e.bucket,ok:e.object,fn:e.file.name}),e.ri=m.default.create(),-1!=this._findUploadIndex()){var o=this;this._state=a.VODSTATE.START,setTimeout(function(){o.nextUpload()},100)}}},{key:"_complete",value:function(e,t){if(e.state=a.UPLOADSTATE.SUCCESS,this.options.onUploadSucceed)try{this.options.onUploadSucceed(e)}catch(e){console.log(e)}var n=0;t&&t.res&&t.res.headers&&(n=t.res.headers["x-oss-request-id"]),this._removeCheckoutpoint(e);var r=this;setTimeout(function(){r.nextUpload()},100),this._retryCount=0,this._reportLog("20003",e,{requestId:n})}},{key:"_progress",value:function(e,t,n){if(this.options.onUploadProgress)try{e.loaded=t.loaded,this.options.onUploadProgress(e,t.total,t.loaded)}catch(e){console.log(e)}var r=t.checkpoint,o=0;r&&(e.checkpoint=r,this._saveCheckoutpoint(e,r,a.UPLOADSTATE.UPLOADING),o=r.uploadId),this._retryCount=0;var i=this._getPortNumber(r),s=0;if(n&&n.headers&&(s=n.headers["x-oss-request-id"]),0!=t.loaded&&this._reportLog("20007",e,{pn:i,requestId:s}),1!=t.loaded&&this._reportLog("20005",e,{UploadId:o,pn:i+1,pr:e.retry?1:0,fs:e.file.size,bu:e.bucket,ok:e.object,fn:e.file.name}),!this._invalidUserId&&!e.isImage&&"vod"==this._ut&&this.options.enableUploadProgress){var u={file:e.file,checkpoint:t,userId:this.options.userId,videoId:e.videoId,region:this.options.region,fileHash:e.fileHash};try{var c=this;A.default.upload(u,function(){},function(e){if((e=JSON.parse(e))&&"InvalidParameter"==e.Code&&e.Message.indexOf("UserId")>0){c._invalidUserId=!0;var t=e.Message+"，正确账号ID(userId)请参考：https://help.aliyun.com/knowledge_detail/37196.html";console.log(t)}})}catch(e){console.log(e)}}}},{key:"_getPortNumber",value:function(e){if(e){var t=e.doneParts;if(t&&t.length>0)return t[t.length-1].number}return 0}},{key:"_removeCheckoutpoint",value:function(e){var t=this._getCheckoutpointKey(e);d.default.remove(t)}},{key:"_getCheckoutpoint",value:function(e){var t=this._getCheckoutpointKey(e),n=d.default.get(t);if(n)try{return JSON.parse(n)}catch(e){}return""}},{key:"_saveCheckoutpoint",value:function(e,t,n){if(t){var r=this._getCheckoutpointKey(e),o=e.file,i={fileName:o.name,lastModified:o.lastModified,size:o.size,object:e.object,videoId:e.videoId,bucket:e.bucket,endpoint:e.endpoint,checkpoint:t,loaded:e.loaded,state:n};d.default.set(r,JSON.stringify(i))}}},{key:"_changeState",value:function(e,t){var n=this._getCheckoutpoint(e);n&&((this._onbeforeunload=!0)&&(t=a.UPLOADSTATE.STOPED),this._saveCheckoutpoint(e,n.checkpoint,t))}},{key:"_getCheckoutpointKey",value:function(e){return"upload_"+e.file.lastModified+"_"+e.file.name+"_"+e.file.size}},{key:"_getCheckoutpointFromCloud",value:function(e,t,n){var r={userId:this.options.userId,uploadInfoList:[{FileName:e.file.name,FileSize:e.file.size,FileCreateTime:e.file.lastModified,FileHash:e.fileHash}],region:this.options.region};A.default.get(r,function(e){t(e)},n)}},{key:"_reportLog",value:function(e,t,n){n||(n={}),n.ri=t.ri,this._ut&&(n.ut=this._ut),this._log.log(e,n)}},{key:"_initEvent",value:function(){var e=this;window&&(window.onbeforeunload=function(t){if(e._onbeforeunload=!0,-1!=e._curIndex&&e._uploadList.length>e._curIndex){var n=e._uploadList[e._curIndex];e._changeState(n,a.UPLOADSTATE.STOPED)}})}},{key:"_initState",value:function(){for(var e=0;e<this._uploadList.length;e++){var t=this._uploadList[e];t.state!=a.UPLOADSTATE.FAIlURE&&t.state!=a.UPLOADSTATE.STOPED||(t.state=a.UPLOADSTATE.INIT)}this._state=a.VODSTATE.INIT}},{key:"_adjustPartSize",value:function(e){return e.file.size/this.options.partSize>1e4?e.file.size/9999:this.options.partSize}}]),e}());t.default=E},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});t.UPLOADSTATE={INIT:"Ready",UPLOADING:"Uploading",SUCCESS:"Success",FAIlURE:"Failure",CANCELED:"Canceled",STOPED:"Stoped"},t.VODSTATE={INIT:"Init",START:"Start",STOP:"Stop",FAILURE:"Failure",EXPIRE:"Expire",END:"End"}},function(e,t,n){"use strict";function r(e){return e&&e.__esModule?e:{default:e}}function o(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}Object.defineProperty(t,"__esModule",{value:!0});var i=function(){function e(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(t,n,r){return n&&e(t.prototype,n),r&&e(t,r),t}}(),a=n(3),s=n(17),u=(r(s),n(1)),c=(r(u),function(){function e(t,n){if(o(this,e),t){this._config=t,this.create(this._config),this._uploadInfo=null,this._callback={};var r=function(){};this._callback.onerror=n.onerror||r,this._callback.oncomplete=n.oncomplete||r,this._callback.onprogress=n.onprogress||r}}return i(e,[{key:"create",value:function(e){if(e.endpoint=e.endpoint||this._config.endpoint,e.bucket=e.bucket||this._config.bucket,!(e.AccessKeyId&&e.AccessKeySecret&&e.endpoint&&e.SecurityToken))throw new Error("AccessKeyId、AccessKeySecret、endpoint should not be null");var t={accessKeyId:e.AccessKeyId,accessKeySecret:e.AccessKeySecret,stsToken:e.SecurityToken,endpoint:e.endpoint||this._config.endpoint,bucket:e.bucket||this._config.bucket,secure:!0,cname:e.cname};e.timeout&&(t.timeout=e.timeout),this.oss=new OSS(t)}},{key:"abort",value:function(e){if(e.checkpoint){var t=e.checkpoint.uploadId;this.oss.abortMultipartUpload(e.object,t)}}},{key:"getVersion",value:function(){}},{key:"cancel",value:function(){this.oss.cancel&&this.oss.cancel()}},{key:"upload",value:function(e,t){this._uploadInfo=e;var n=this,r=function(e,t,o){r=n._progress(e,t,o)},o={parallel:t.parallel||this._config.parallel||a.UPLOADDEFAULT.PARALLEL,partSize:Math.round(t.partSize||this._config.partSize||a.UPLOADDEFAULT.PARTSIZE),progress:r};t.headers&&(o.headers=t.headers),e.checkpoint&&(o.checkpoint=e.checkpoint),e.bucket||(this.oss.options.bucket=e.bucket),e.endpoint||(this.oss.options.endpoint=e.endpoint),this.oss.multipartUpload(e.object,e.file,o).then(function(e,t){n._complete(e)}).catch(function(e){n.oss.cancel&&(n.oss&&n.oss.isCancel()?console.log("oss is cancel as error"):n.oss.cancel()),n._error(e)})}},{key:"header",value:function(e,t,n){this.oss.get(e.object).then(function(e){t(e)}).catch(function(e){n(e)})}},{key:"_progress",value:function(e,t,n){this._callback.onprogress(this._uploadInfo,{loaded:e,total:this._uploadInfo.file.size,checkpoint:t},n)}},{key:"_error",value:function(e){this._callback.onerror(this._uploadInfo,e)}},{key:"_complete",value:function(e){this._callback.oncomplete(this._uploadInfo,e)}}]),e}());t.default=c},function(e,t,n){"use strict";function r(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}Object.defineProperty(t,"__esModule",{value:!0});var o=function(){function e(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(t,n,r){return n&&e(t.prototype,n),r&&e(t,r),t}}(),i=function(){function e(){r(this,e)}return o(e,null,[{key:"format",value:function(e){if(arguments.length<2)return null;for(var t=arguments[1],n=1;n<arguments.length;n++){var r=new RegExp("\\{"+(n-1)+"\\}","gm");t=t.replace(r,arguments[n+1])}return{code:e,message:t}}},{key:"CODE",get:function(){return{SUCCESS:"Successful",EmptyValue:"InvalidParameter.EmptyValue",STSInvalid:"InvalidParameter.TokenInvalid",ReadFileError:"ReadFileError",FILEDUPLICATION:"FileDuplication",UploadALEADRYSTARTED:"UploadAlearyStarted"}}},{key:"MESSAGE",get:function(){return{SUCCESS:"Successful",EmptyValue:"参数 {0} 不能为空。",STSInvalid:"STS参数非法， accessKeyId、accessKeySecret、secretToken、expireTime都不能为空。",ReadFileError:"读取文件{0}{1}失败.",FILEDUPLICATION:"文件重复添加 {0}",UploadALEADRYSTARTED:"重复开始."}}}]),e}();t.default=i},function(e,t,n){"use strict";(function(e){function n(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}Object.defineProperty(t,"__esModule",{value:!0});var r=function(){function e(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(t,n,r){return n&&e(t.prototype,n),r&&e(t,r),t}}(),o=function(){function t(){n(this,t)}return r(t,null,[{key:"encode",value:function(t){return new e(t).toString("base64")}},{key:"decode",value:function(t){return new e(t,"base64").toString()}}]),t}();t.default=o}).call(t,n(19).Buffer)},function(e,t,n){"use strict";(function(e){function r(){return i.TYPED_ARRAY_SUPPORT?2147483647:1073741823}function o(e,t){if(r()<t)throw new RangeError("Invalid typed array length");return i.TYPED_ARRAY_SUPPORT?(e=new Uint8Array(t),e.__proto__=i.prototype):(null===e&&(e=new i(t)),e.length=t),e}function i(e,t,n){if(!(i.TYPED_ARRAY_SUPPORT||this instanceof i))return new i(e,t,n);if("number"==typeof e){if("string"==typeof t)throw new Error("If encoding is specified then the first argument must be a string");return c(this,e)}return a(this,e,t,n)}function a(e,t,n,r){if("number"==typeof t)throw new TypeError('"value" argument must not be a number');return"undefined"!=typeof ArrayBuffer&&t instanceof ArrayBuffer?h(e,t,n,r):"string"==typeof t?l(e,t,n):d(e,t)}function s(e){if("number"!=typeof e)throw new TypeError('"size" argument must be a number');if(e<0)throw new RangeError('"size" argument must not be negative')}function u(e,t,n,r){return s(t),t<=0?o(e,t):void 0!==n?"string"==typeof r?o(e,t).fill(n,r):o(e,t).fill(n):o(e,t)}function c(e,t){if(s(t),e=o(e,t<0?0:0|p(t)),!i.TYPED_ARRAY_SUPPORT)for(var n=0;n<t;++n)e[n]=0;return e}function l(e,t,n){if("string"==typeof n&&""!==n||(n="utf8"),!i.isEncoding(n))throw new TypeError('"encoding" must be a valid string encoding');var r=0|v(t,n);e=o(e,r);var a=e.write(t,n);return a!==r&&(e=e.slice(0,a)),e}function f(e,t){var n=t.length<0?0:0|p(t.length);e=o(e,n);for(var r=0;r<n;r+=1)e[r]=255&t[r];return e}function h(e,t,n,r){if(t.byteLength,n<0||t.byteLength<n)throw new RangeError("'offset' is out of bounds");if(t.byteLength<n+(r||0))throw new RangeError("'length' is out of bounds");return t=void 0===n&&void 0===r?new Uint8Array(t):void 0===r?new Uint8Array(t,n):new Uint8Array(t,n,r),i.TYPED_ARRAY_SUPPORT?(e=t,e.__proto__=i.prototype):e=f(e,t),e}function d(e,t){if(i.isBuffer(t)){var n=0|p(t.length);return e=o(e,n),0===e.length?e:(t.copy(e,0,0,n),e)}if(t){if("undefined"!=typeof ArrayBuffer&&t.buffer instanceof ArrayBuffer||"length"in t)return"number"!=typeof t.length||G(t.length)?o(e,0):f(e,t);if("Buffer"===t.type&&Q(t.data))return f(e,t.data)}throw new TypeError("First argument must be a string, Buffer, ArrayBuffer, Array, or array-like object.")}function p(e){if(e>=r())throw new RangeError("Attempt to allocate Buffer larger than maximum size: 0x"+r().toString(16)+" bytes");return 0|e}function g(e){return+e!=e&&(e=0),i.alloc(+e)}function v(e,t){if(i.isBuffer(e))return e.length;if("undefined"!=typeof ArrayBuffer&&"function"==typeof ArrayBuffer.isView&&(ArrayBuffer.isView(e)||e instanceof ArrayBuffer))return e.byteLength;"string"!=typeof e&&(e=""+e);var n=e.length;if(0===n)return 0;for(var r=!1;;)switch(t){case"ascii":case"latin1":case"binary":return n;case"utf8":case"utf-8":case void 0:return H(e).length;case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return 2*n;case"hex":return n>>>1;case"base64":return q(e).length;default:if(r)return H(e).length;t=(""+t).toLowerCase(),r=!0}}function y(e,t,n){var r=!1;if((void 0===t||t<0)&&(t=0),t>this.length)return"";if((void 0===n||n>this.length)&&(n=this.length),n<=0)return"";if(n>>>=0,t>>>=0,n<=t)return"";for(e||(e="utf8");;)switch(e){case"hex":return L(this,t,n);case"utf8":case"utf-8":return U(this,t,n);case"ascii":return O(this,t,n);case"latin1":case"binary":return C(this,t,n);case"base64":return k(this,t,n);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return R(this,t,n);default:if(r)throw new TypeError("Unknown encoding: "+e);e=(e+"").toLowerCase(),r=!0}}function _(e,t,n){var r=e[t];e[t]=e[n],e[n]=r}function m(e,t,n,r,o){if(0===e.length)return-1;if("string"==typeof n?(r=n,n=0):n>2147483647?n=2147483647:n<-2147483648&&(n=-2147483648),n=+n,isNaN(n)&&(n=o?0:e.length-1),n<0&&(n=e.length+n),n>=e.length){if(o)return-1;n=e.length-1}else if(n<0){if(!o)return-1;n=0}if("string"==typeof t&&(t=i.from(t,r)),i.isBuffer(t))return 0===t.length?-1:T(e,t,n,r,o);if("number"==typeof t)return t&=255,i.TYPED_ARRAY_SUPPORT&&"function"==typeof Uint8Array.prototype.indexOf?o?Uint8Array.prototype.indexOf.call(e,t,n):Uint8Array.prototype.lastIndexOf.call(e,t,n):T(e,[t],n,r,o);throw new TypeError("val must be string, number or Buffer")}function T(e,t,n,r,o){function i(e,t){return 1===a?e[t]:e.readUInt16BE(t*a)}var a=1,s=e.length,u=t.length;if(void 0!==r&&("ucs2"===(r=String(r).toLowerCase())||"ucs-2"===r||"utf16le"===r||"utf-16le"===r)){if(e.length<2||t.length<2)return-1;a=2,s/=2,u/=2,n/=2}var c;if(o){var l=-1;for(c=n;c<s;c++)if(i(e,c)===i(t,-1===l?0:c-l)){if(-1===l&&(l=c),c-l+1===u)return l*a}else-1!==l&&(c-=c-l),l=-1}else for(n+u>s&&(n=s-u),c=n;c>=0;c--){for(var f=!0,h=0;h<u;h++)if(i(e,c+h)!==i(t,h)){f=!1;break}if(f)return c}return-1}function S(e,t,n,r){n=Number(n)||0;var o=e.length-n;r?(r=Number(r))>o&&(r=o):r=o;var i=t.length;if(i%2!=0)throw new TypeError("Invalid hex string");r>i/2&&(r=i/2);for(var a=0;a<r;++a){var s=parseInt(t.substr(2*a,2),16);if(isNaN(s))return a;e[n+a]=s}return a}function w(e,t,n,r){return J(H(t,e.length-n),e,n,r)}function A(e,t,n,r){return J(Y(t),e,n,r)}function b(e,t,n,r){return A(e,t,n,r)}function I(e,t,n,r){return J(q(t),e,n,r)}function E(e,t,n,r){return J(W(t,e.length-n),e,n,r)}function k(e,t,n){return 0===t&&n===e.length?X.fromByteArray(e):X.fromByteArray(e.slice(t,n))}function U(e,t,n){n=Math.min(e.length,n);for(var r=[],o=t;o<n;){var i=e[o],a=null,s=i>239?4:i>223?3:i>191?2:1;if(o+s<=n){var u,c,l,f;switch(s){case 1:i<128&&(a=i);break;case 2:u=e[o+1],128==(192&u)&&(f=(31&i)<<6|63&u)>127&&(a=f);break;case 3:u=e[o+1],c=e[o+2],128==(192&u)&&128==(192&c)&&(f=(15&i)<<12|(63&u)<<6|63&c)>2047&&(f<55296||f>57343)&&(a=f);break;case 4:u=e[o+1],c=e[o+2],l=e[o+3],128==(192&u)&&128==(192&c)&&128==(192&l)&&(f=(15&i)<<18|(63&u)<<12|(63&c)<<6|63&l)>65535&&f<1114112&&(a=f)}}null===a?(a=65533,s=1):a>65535&&(a-=65536,r.push(a>>>10&1023|55296),a=56320|1023&a),r.push(a),o+=s}return P(r)}function P(e){var t=e.length;if(t<=$)return String.fromCharCode.apply(String,e);for(var n="",r=0;r<t;)n+=String.fromCharCode.apply(String,e.slice(r,r+=$));return n}function O(e,t,n){var r="";n=Math.min(e.length,n);for(var o=t;o<n;++o)r+=String.fromCharCode(127&e[o]);return r}function C(e,t,n){var r="";n=Math.min(e.length,n);for(var o=t;o<n;++o)r+=String.fromCharCode(e[o]);return r}function L(e,t,n){var r=e.length;(!t||t<0)&&(t=0),(!n||n<0||n>r)&&(n=r);for(var o="",i=t;i<n;++i)o+=V(e[i]);return o}function R(e,t,n){for(var r=e.slice(t,n),o="",i=0;i<r.length;i+=2)o+=String.fromCharCode(r[i]+256*r[i+1]);return o}function D(e,t,n){if(e%1!=0||e<0)throw new RangeError("offset is not uint");if(e+t>n)throw new RangeError("Trying to access beyond buffer length")}function x(e,t,n,r,o,a){if(!i.isBuffer(e))throw new TypeError('"buffer" argument must be a Buffer instance');if(t>o||t<a)throw new RangeError('"value" argument is out of bounds');if(n+r>e.length)throw new RangeError("Index out of range")}function M(e,t,n,r){t<0&&(t=65535+t+1);for(var o=0,i=Math.min(e.length-n,2);o<i;++o)e[n+o]=(t&255<<8*(r?o:1-o))>>>8*(r?o:1-o)}function B(e,t,n,r){t<0&&(t=4294967295+t+1);for(var o=0,i=Math.min(e.length-n,4);o<i;++o)e[n+o]=t>>>8*(r?o:3-o)&255}function N(e,t,n,r,o,i){if(n+r>e.length)throw new RangeError("Index out of range");if(n<0)throw new RangeError("Index out of range")}function F(e,t,n,r,o){return o||N(e,t,n,4,3.4028234663852886e38,-3.4028234663852886e38),Z.write(e,t,n,r,23,4),n+4}function j(e,t,n,r,o){return o||N(e,t,n,8,1.7976931348623157e308,-1.7976931348623157e308),Z.write(e,t,n,r,52,8),n+8}function K(e){if(e=z(e).replace(ee,""),e.length<2)return"";for(;e.length%4!=0;)e+="=";return e}function z(e){return e.trim?e.trim():e.replace(/^\s+|\s+$/g,"")}function V(e){return e<16?"0"+e.toString(16):e.toString(16)}function H(e,t){t=t||1/0;for(var n,r=e.length,o=null,i=[],a=0;a<r;++a){if((n=e.charCodeAt(a))>55295&&n<57344){if(!o){if(n>56319){(t-=3)>-1&&i.push(239,191,189);continue}if(a+1===r){(t-=3)>-1&&i.push(239,191,189);continue}o=n;continue}if(n<56320){(t-=3)>-1&&i.push(239,191,189),o=n;continue}n=65536+(o-55296<<10|n-56320)}else o&&(t-=3)>-1&&i.push(239,191,189);if(o=null,n<128){if((t-=1)<0)break;i.push(n)}else if(n<2048){if((t-=2)<0)break;i.push(n>>6|192,63&n|128)}else if(n<65536){if((t-=3)<0)break;i.push(n>>12|224,n>>6&63|128,63&n|128)}else{if(!(n<1114112))throw new Error("Invalid code point");if((t-=4)<0)break;i.push(n>>18|240,n>>12&63|128,n>>6&63|128,63&n|128)}}return i}function Y(e){for(var t=[],n=0;n<e.length;++n)t.push(255&e.charCodeAt(n));return t}function W(e,t){for(var n,r,o,i=[],a=0;a<e.length&&!((t-=2)<0);++a)n=e.charCodeAt(a),r=n>>8,o=n%256,i.push(o),i.push(r);return i}function q(e){return X.toByteArray(K(e))}function J(e,t,n,r){for(var o=0;o<r&&!(o+n>=t.length||o>=e.length);++o)t[o+n]=e[o];return o}function G(e){return e!==e}/*!
 * The buffer module from node.js, for the browser.
 *
 * <AUTHOR> Aboukhadijeh <http://feross.org>
 * @license  MIT
 */
var X=n(21),Z=n(22),Q=n(23);t.Buffer=i,t.SlowBuffer=g,t.INSPECT_MAX_BYTES=50,i.TYPED_ARRAY_SUPPORT=void 0!==e.TYPED_ARRAY_SUPPORT?e.TYPED_ARRAY_SUPPORT:function(){try{var e=new Uint8Array(1);return e.__proto__={__proto__:Uint8Array.prototype,foo:function(){return 42}},42===e.foo()&&"function"==typeof e.subarray&&0===e.subarray(1,1).byteLength}catch(e){return!1}}(),t.kMaxLength=r(),i.poolSize=8192,i._augment=function(e){return e.__proto__=i.prototype,e},i.from=function(e,t,n){return a(null,e,t,n)},i.TYPED_ARRAY_SUPPORT&&(i.prototype.__proto__=Uint8Array.prototype,i.__proto__=Uint8Array,"undefined"!=typeof Symbol&&Symbol.species&&i[Symbol.species]===i&&Object.defineProperty(i,Symbol.species,{value:null,configurable:!0})),i.alloc=function(e,t,n){return u(null,e,t,n)},i.allocUnsafe=function(e){return c(null,e)},i.allocUnsafeSlow=function(e){return c(null,e)},i.isBuffer=function(e){return!(null==e||!e._isBuffer)},i.compare=function(e,t){if(!i.isBuffer(e)||!i.isBuffer(t))throw new TypeError("Arguments must be Buffers");if(e===t)return 0;for(var n=e.length,r=t.length,o=0,a=Math.min(n,r);o<a;++o)if(e[o]!==t[o]){n=e[o],r=t[o];break}return n<r?-1:r<n?1:0},i.isEncoding=function(e){switch(String(e).toLowerCase()){case"hex":case"utf8":case"utf-8":case"ascii":case"latin1":case"binary":case"base64":case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return!0;default:return!1}},i.concat=function(e,t){if(!Q(e))throw new TypeError('"list" argument must be an Array of Buffers');if(0===e.length)return i.alloc(0);var n;if(void 0===t)for(t=0,n=0;n<e.length;++n)t+=e[n].length;var r=i.allocUnsafe(t),o=0;for(n=0;n<e.length;++n){var a=e[n];if(!i.isBuffer(a))throw new TypeError('"list" argument must be an Array of Buffers');a.copy(r,o),o+=a.length}return r},i.byteLength=v,i.prototype._isBuffer=!0,i.prototype.swap16=function(){var e=this.length;if(e%2!=0)throw new RangeError("Buffer size must be a multiple of 16-bits");for(var t=0;t<e;t+=2)_(this,t,t+1);return this},i.prototype.swap32=function(){var e=this.length;if(e%4!=0)throw new RangeError("Buffer size must be a multiple of 32-bits");for(var t=0;t<e;t+=4)_(this,t,t+3),_(this,t+1,t+2);return this},i.prototype.swap64=function(){var e=this.length;if(e%8!=0)throw new RangeError("Buffer size must be a multiple of 64-bits");for(var t=0;t<e;t+=8)_(this,t,t+7),_(this,t+1,t+6),_(this,t+2,t+5),_(this,t+3,t+4);return this},i.prototype.toString=function(){var e=0|this.length;return 0===e?"":0===arguments.length?U(this,0,e):y.apply(this,arguments)},i.prototype.equals=function(e){if(!i.isBuffer(e))throw new TypeError("Argument must be a Buffer");return this===e||0===i.compare(this,e)},i.prototype.inspect=function(){var e="",n=t.INSPECT_MAX_BYTES;return this.length>0&&(e=this.toString("hex",0,n).match(/.{2}/g).join(" "),this.length>n&&(e+=" ... ")),"<Buffer "+e+">"},i.prototype.compare=function(e,t,n,r,o){if(!i.isBuffer(e))throw new TypeError("Argument must be a Buffer");if(void 0===t&&(t=0),void 0===n&&(n=e?e.length:0),void 0===r&&(r=0),void 0===o&&(o=this.length),t<0||n>e.length||r<0||o>this.length)throw new RangeError("out of range index");if(r>=o&&t>=n)return 0;if(r>=o)return-1;if(t>=n)return 1;if(t>>>=0,n>>>=0,r>>>=0,o>>>=0,this===e)return 0;for(var a=o-r,s=n-t,u=Math.min(a,s),c=this.slice(r,o),l=e.slice(t,n),f=0;f<u;++f)if(c[f]!==l[f]){a=c[f],s=l[f];break}return a<s?-1:s<a?1:0},i.prototype.includes=function(e,t,n){return-1!==this.indexOf(e,t,n)},i.prototype.indexOf=function(e,t,n){return m(this,e,t,n,!0)},i.prototype.lastIndexOf=function(e,t,n){return m(this,e,t,n,!1)},i.prototype.write=function(e,t,n,r){if(void 0===t)r="utf8",n=this.length,t=0;else if(void 0===n&&"string"==typeof t)r=t,n=this.length,t=0;else{if(!isFinite(t))throw new Error("Buffer.write(string, encoding, offset[, length]) is no longer supported");t|=0,isFinite(n)?(n|=0,void 0===r&&(r="utf8")):(r=n,n=void 0)}var o=this.length-t;if((void 0===n||n>o)&&(n=o),e.length>0&&(n<0||t<0)||t>this.length)throw new RangeError("Attempt to write outside buffer bounds");r||(r="utf8");for(var i=!1;;)switch(r){case"hex":return S(this,e,t,n);case"utf8":case"utf-8":return w(this,e,t,n);case"ascii":return A(this,e,t,n);case"latin1":case"binary":return b(this,e,t,n);case"base64":return I(this,e,t,n);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return E(this,e,t,n);default:if(i)throw new TypeError("Unknown encoding: "+r);r=(""+r).toLowerCase(),i=!0}},i.prototype.toJSON=function(){return{type:"Buffer",data:Array.prototype.slice.call(this._arr||this,0)}};var $=4096;i.prototype.slice=function(e,t){var n=this.length;e=~~e,t=void 0===t?n:~~t,e<0?(e+=n)<0&&(e=0):e>n&&(e=n),t<0?(t+=n)<0&&(t=0):t>n&&(t=n),t<e&&(t=e);var r;if(i.TYPED_ARRAY_SUPPORT)r=this.subarray(e,t),r.__proto__=i.prototype;else{var o=t-e;r=new i(o,void 0);for(var a=0;a<o;++a)r[a]=this[a+e]}return r},i.prototype.readUIntLE=function(e,t,n){e|=0,t|=0,n||D(e,t,this.length);for(var r=this[e],o=1,i=0;++i<t&&(o*=256);)r+=this[e+i]*o;return r},i.prototype.readUIntBE=function(e,t,n){e|=0,t|=0,n||D(e,t,this.length);for(var r=this[e+--t],o=1;t>0&&(o*=256);)r+=this[e+--t]*o;return r},i.prototype.readUInt8=function(e,t){return t||D(e,1,this.length),this[e]},i.prototype.readUInt16LE=function(e,t){return t||D(e,2,this.length),this[e]|this[e+1]<<8},i.prototype.readUInt16BE=function(e,t){return t||D(e,2,this.length),this[e]<<8|this[e+1]},i.prototype.readUInt32LE=function(e,t){return t||D(e,4,this.length),(this[e]|this[e+1]<<8|this[e+2]<<16)+16777216*this[e+3]},i.prototype.readUInt32BE=function(e,t){return t||D(e,4,this.length),16777216*this[e]+(this[e+1]<<16|this[e+2]<<8|this[e+3])},i.prototype.readIntLE=function(e,t,n){e|=0,t|=0,n||D(e,t,this.length);for(var r=this[e],o=1,i=0;++i<t&&(o*=256);)r+=this[e+i]*o;return o*=128,r>=o&&(r-=Math.pow(2,8*t)),r},i.prototype.readIntBE=function(e,t,n){e|=0,t|=0,n||D(e,t,this.length);for(var r=t,o=1,i=this[e+--r];r>0&&(o*=256);)i+=this[e+--r]*o;return o*=128,i>=o&&(i-=Math.pow(2,8*t)),i},i.prototype.readInt8=function(e,t){return t||D(e,1,this.length),128&this[e]?-1*(255-this[e]+1):this[e]},i.prototype.readInt16LE=function(e,t){t||D(e,2,this.length);var n=this[e]|this[e+1]<<8;return 32768&n?4294901760|n:n},i.prototype.readInt16BE=function(e,t){t||D(e,2,this.length);var n=this[e+1]|this[e]<<8;return 32768&n?4294901760|n:n},i.prototype.readInt32LE=function(e,t){return t||D(e,4,this.length),this[e]|this[e+1]<<8|this[e+2]<<16|this[e+3]<<24},i.prototype.readInt32BE=function(e,t){return t||D(e,4,this.length),this[e]<<24|this[e+1]<<16|this[e+2]<<8|this[e+3]},i.prototype.readFloatLE=function(e,t){return t||D(e,4,this.length),Z.read(this,e,!0,23,4)},i.prototype.readFloatBE=function(e,t){return t||D(e,4,this.length),Z.read(this,e,!1,23,4)},i.prototype.readDoubleLE=function(e,t){return t||D(e,8,this.length),Z.read(this,e,!0,52,8)},i.prototype.readDoubleBE=function(e,t){return t||D(e,8,this.length),Z.read(this,e,!1,52,8)},i.prototype.writeUIntLE=function(e,t,n,r){if(e=+e,t|=0,n|=0,!r){x(this,e,t,n,Math.pow(2,8*n)-1,0)}var o=1,i=0;for(this[t]=255&e;++i<n&&(o*=256);)this[t+i]=e/o&255;return t+n},i.prototype.writeUIntBE=function(e,t,n,r){if(e=+e,t|=0,n|=0,!r){x(this,e,t,n,Math.pow(2,8*n)-1,0)}var o=n-1,i=1;for(this[t+o]=255&e;--o>=0&&(i*=256);)this[t+o]=e/i&255;return t+n},i.prototype.writeUInt8=function(e,t,n){return e=+e,t|=0,n||x(this,e,t,1,255,0),i.TYPED_ARRAY_SUPPORT||(e=Math.floor(e)),this[t]=255&e,t+1},i.prototype.writeUInt16LE=function(e,t,n){return e=+e,t|=0,n||x(this,e,t,2,65535,0),i.TYPED_ARRAY_SUPPORT?(this[t]=255&e,this[t+1]=e>>>8):M(this,e,t,!0),t+2},i.prototype.writeUInt16BE=function(e,t,n){return e=+e,t|=0,n||x(this,e,t,2,65535,0),i.TYPED_ARRAY_SUPPORT?(this[t]=e>>>8,this[t+1]=255&e):M(this,e,t,!1),t+2},i.prototype.writeUInt32LE=function(e,t,n){return e=+e,t|=0,n||x(this,e,t,4,4294967295,0),i.TYPED_ARRAY_SUPPORT?(this[t+3]=e>>>24,this[t+2]=e>>>16,this[t+1]=e>>>8,this[t]=255&e):B(this,e,t,!0),t+4},i.prototype.writeUInt32BE=function(e,t,n){return e=+e,t|=0,n||x(this,e,t,4,4294967295,0),i.TYPED_ARRAY_SUPPORT?(this[t]=e>>>24,this[t+1]=e>>>16,this[t+2]=e>>>8,this[t+3]=255&e):B(this,e,t,!1),t+4},i.prototype.writeIntLE=function(e,t,n,r){if(e=+e,t|=0,!r){var o=Math.pow(2,8*n-1);x(this,e,t,n,o-1,-o)}var i=0,a=1,s=0;for(this[t]=255&e;++i<n&&(a*=256);)e<0&&0===s&&0!==this[t+i-1]&&(s=1),this[t+i]=(e/a>>0)-s&255;return t+n},i.prototype.writeIntBE=function(e,t,n,r){if(e=+e,t|=0,!r){var o=Math.pow(2,8*n-1);x(this,e,t,n,o-1,-o)}var i=n-1,a=1,s=0;for(this[t+i]=255&e;--i>=0&&(a*=256);)e<0&&0===s&&0!==this[t+i+1]&&(s=1),this[t+i]=(e/a>>0)-s&255;return t+n},i.prototype.writeInt8=function(e,t,n){return e=+e,t|=0,n||x(this,e,t,1,127,-128),i.TYPED_ARRAY_SUPPORT||(e=Math.floor(e)),e<0&&(e=255+e+1),this[t]=255&e,t+1},i.prototype.writeInt16LE=function(e,t,n){return e=+e,t|=0,n||x(this,e,t,2,32767,-32768),i.TYPED_ARRAY_SUPPORT?(this[t]=255&e,this[t+1]=e>>>8):M(this,e,t,!0),t+2},i.prototype.writeInt16BE=function(e,t,n){return e=+e,t|=0,n||x(this,e,t,2,32767,-32768),i.TYPED_ARRAY_SUPPORT?(this[t]=e>>>8,this[t+1]=255&e):M(this,e,t,!1),t+2},i.prototype.writeInt32LE=function(e,t,n){return e=+e,t|=0,n||x(this,e,t,4,2147483647,-2147483648),i.TYPED_ARRAY_SUPPORT?(this[t]=255&e,this[t+1]=e>>>8,this[t+2]=e>>>16,this[t+3]=e>>>24):B(this,e,t,!0),t+4},i.prototype.writeInt32BE=function(e,t,n){return e=+e,t|=0,n||x(this,e,t,4,2147483647,-2147483648),e<0&&(e=4294967295+e+1),i.TYPED_ARRAY_SUPPORT?(this[t]=e>>>24,this[t+1]=e>>>16,this[t+2]=e>>>8,this[t+3]=255&e):B(this,e,t,!1),t+4},i.prototype.writeFloatLE=function(e,t,n){return F(this,e,t,!0,n)},i.prototype.writeFloatBE=function(e,t,n){return F(this,e,t,!1,n)},i.prototype.writeDoubleLE=function(e,t,n){return j(this,e,t,!0,n)},i.prototype.writeDoubleBE=function(e,t,n){return j(this,e,t,!1,n)},i.prototype.copy=function(e,t,n,r){if(n||(n=0),r||0===r||(r=this.length),t>=e.length&&(t=e.length),t||(t=0),r>0&&r<n&&(r=n),r===n)return 0;if(0===e.length||0===this.length)return 0;if(t<0)throw new RangeError("targetStart out of bounds");if(n<0||n>=this.length)throw new RangeError("sourceStart out of bounds");if(r<0)throw new RangeError("sourceEnd out of bounds");r>this.length&&(r=this.length),e.length-t<r-n&&(r=e.length-t+n);var o,a=r-n;if(this===e&&n<t&&t<r)for(o=a-1;o>=0;--o)e[o+t]=this[o+n];else if(a<1e3||!i.TYPED_ARRAY_SUPPORT)for(o=0;o<a;++o)e[o+t]=this[o+n];else Uint8Array.prototype.set.call(e,this.subarray(n,n+a),t);return a},i.prototype.fill=function(e,t,n,r){if("string"==typeof e){if("string"==typeof t?(r=t,t=0,n=this.length):"string"==typeof n&&(r=n,n=this.length),1===e.length){var o=e.charCodeAt(0);o<256&&(e=o)}if(void 0!==r&&"string"!=typeof r)throw new TypeError("encoding must be a string");if("string"==typeof r&&!i.isEncoding(r))throw new TypeError("Unknown encoding: "+r)}else"number"==typeof e&&(e&=255);if(t<0||this.length<t||this.length<n)throw new RangeError("Out of range index");if(n<=t)return this;t>>>=0,n=void 0===n?this.length:n>>>0,e||(e=0);var a;if("number"==typeof e)for(a=t;a<n;++a)this[a]=e;else{var s=i.isBuffer(e)?e:H(new i(e,r).toString()),u=s.length;for(a=0;a<n-t;++a)this[a+t]=s[a%u]}return this};var ee=/[^+\/0-9A-Za-z-_]/g}).call(t,n(20))},function(e,t){var n;n=function(){return this}();try{n=n||Function("return this")()||(0,eval)("this")}catch(e){"object"==typeof window&&(n=window)}e.exports=n},function(e,t,n){"use strict";function r(e){var t=e.length;if(t%4>0)throw new Error("Invalid string. Length must be a multiple of 4");var n=e.indexOf("=");return-1===n&&(n=t),[n,n===t?0:4-n%4]}function o(e){var t=r(e),n=t[0],o=t[1];return 3*(n+o)/4-o}function i(e,t,n){return 3*(t+n)/4-n}function a(e){var t,n,o=r(e),a=o[0],s=o[1],u=new h(i(e,a,s)),c=0,l=s>0?a-4:a;for(n=0;n<l;n+=4)t=f[e.charCodeAt(n)]<<18|f[e.charCodeAt(n+1)]<<12|f[e.charCodeAt(n+2)]<<6|f[e.charCodeAt(n+3)],u[c++]=t>>16&255,u[c++]=t>>8&255,u[c++]=255&t;return 2===s&&(t=f[e.charCodeAt(n)]<<2|f[e.charCodeAt(n+1)]>>4,u[c++]=255&t),1===s&&(t=f[e.charCodeAt(n)]<<10|f[e.charCodeAt(n+1)]<<4|f[e.charCodeAt(n+2)]>>2,u[c++]=t>>8&255,u[c++]=255&t),u}function s(e){return l[e>>18&63]+l[e>>12&63]+l[e>>6&63]+l[63&e]}function u(e,t,n){for(var r,o=[],i=t;i<n;i+=3)r=(e[i]<<16&16711680)+(e[i+1]<<8&65280)+(255&e[i+2]),o.push(s(r));return o.join("")}function c(e){for(var t,n=e.length,r=n%3,o=[],i=0,a=n-r;i<a;i+=16383)o.push(u(e,i,i+16383>a?a:i+16383));return 1===r?(t=e[n-1],o.push(l[t>>2]+l[t<<4&63]+"==")):2===r&&(t=(e[n-2]<<8)+e[n-1],o.push(l[t>>10]+l[t>>4&63]+l[t<<2&63]+"=")),o.join("")}t.byteLength=o,t.toByteArray=a,t.fromByteArray=c;for(var l=[],f=[],h="undefined"!=typeof Uint8Array?Uint8Array:Array,d="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",p=0,g=d.length;p<g;++p)l[p]=d[p],f[d.charCodeAt(p)]=p;f["-".charCodeAt(0)]=62,f["_".charCodeAt(0)]=63},function(e,t){t.read=function(e,t,n,r,o){var i,a,s=8*o-r-1,u=(1<<s)-1,c=u>>1,l=-7,f=n?o-1:0,h=n?-1:1,d=e[t+f];for(f+=h,i=d&(1<<-l)-1,d>>=-l,l+=s;l>0;i=256*i+e[t+f],f+=h,l-=8);for(a=i&(1<<-l)-1,i>>=-l,l+=r;l>0;a=256*a+e[t+f],f+=h,l-=8);if(0===i)i=1-c;else{if(i===u)return a?NaN:1/0*(d?-1:1);a+=Math.pow(2,r),i-=c}return(d?-1:1)*a*Math.pow(2,i-r)},t.write=function(e,t,n,r,o,i){var a,s,u,c=8*i-o-1,l=(1<<c)-1,f=l>>1,h=23===o?Math.pow(2,-24)-Math.pow(2,-77):0,d=r?0:i-1,p=r?1:-1,g=t<0||0===t&&1/t<0?1:0;for(t=Math.abs(t),isNaN(t)||t===1/0?(s=isNaN(t)?1:0,a=l):(a=Math.floor(Math.log(t)/Math.LN2),t*(u=Math.pow(2,-a))<1&&(a--,u*=2),t+=a+f>=1?h/u:h*Math.pow(2,1-f),t*u>=2&&(a++,u/=2),a+f>=l?(s=0,a=l):a+f>=1?(s=(t*u-1)*Math.pow(2,o),a+=f):(s=t*Math.pow(2,f-1)*Math.pow(2,o),a=0));o>=8;e[n+d]=255&s,d+=p,s/=256,o-=8);for(a=a<<o|s,c+=o;c>0;e[n+d]=255&a,d+=p,a/=256,c-=8);e[n+d-p]|=128*g}},function(e,t){var n={}.toString;e.exports=Array.isArray||function(e){return"[object Array]"==n.call(e)}},function(e,t,n){"use strict";function r(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}Object.defineProperty(t,"__esModule",{value:!0});var o=function(){function e(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(t,n,r){return n&&e(t.prototype,n),r&&e(t,r),t}}(),i=function(){function e(){r(this,e)}return o(e,null,[{key:"set",value:function(e,t){try{window.localStorage&&localStorage.setItem(e,t)}catch(n){window[e+"_localStorage"]=t}}},{key:"get",value:function(e){try{if(window.localStorage)return localStorage.getItem(e)}catch(t){return window[e+"_localStorage"]}return""}},{key:"remove",value:function(e){try{window.localStorage&&localStorage.removeItem(e)}catch(t){delete window[e+"_localStorage"]}}}]),e}();t.default=i},function(e,t,n){"use strict";function r(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}Object.defineProperty(t,"__esModule",{value:!0});var o=function(){function e(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(t,n,r){return n&&e(t.prototype,n),r&&e(t,r),t}}(),i=function(){function e(){r(this,e)}return o(e,null,[{key:"get",value:function(e){for(var t=e+"",n=document.cookie.split(";"),r=0;r<n.length;r++){var o=n[r].trim();if(0==o.indexOf(t))return unescape(o.substring(t.length+1,o.length))}return""}},{key:"set",value:function(e,t,n){var r=new Date;r.setTime(r.getTime()+24*n*60*60*1e3);var o="expires="+r.toGMTString();document.cookie=e+"="+escape(t)+"; "+o}}]),e}();t.default=i},function(e,t,n){"use strict";function r(e){return e&&e.__esModule?e:{default:e}}function o(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}Object.defineProperty(t,"__esModule",{value:!0});var i=function(){function e(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(t,n,r){return n&&e(t.prototype,n),r&&e(t,r),t}}(),a=n(8),s=r(a),u=n(9),c=r(u),l=function(){function e(){o(this,e)}return i(e,null,[{key:"refreshUploadAuth",value:function(e,t,n){var r=(c.default.randomUUID(),c.default.randomUUID()),o={AccessKeyId:e.accessKeyId,SecurityToken:e.securityToken,Action:"RefreshUploadVideo",VideoId:e.videoId,Version:"2017-03-21",Format:"JSON",SignatureMethod:"HMAC-SHA1",SignatureVersion:"1.0",SignatureNonce:r,RequestId:e.requestId},i=c.default.makeUTF8sort(o,"=","&")+"&Signature="+c.default.aliyunEncodeURI(c.default.makeChangeSiga(o,e.accessKeySecret)),a="https://vod."+e.region+".aliyuncs.com/?"+i;s.default.get(a,function(e){var e=JSON.parse(e);t&&t(e)},function(e){if(n){var t=JSON.parse(e);n(t)}})}},{key:"getUploadAuth",value:function(e,t,n){var r=(c.default.randomUUID(),c.default.randomUUID()),o={AccessKeyId:e.accessKeyId,SecurityToken:e.securityToken,Action:"CreateUploadVideo",Title:e.title,FileName:e.fileName,Version:"2017-03-21",Format:"JSON",SignatureMethod:"HMAC-SHA1",SignatureVersion:"1.0",SignatureNonce:r,RequestId:e.requestId};e.fileSize&&(o.FileSize=e.fileSize),e.description&&(o.Description=e.description),e.cateId&&(o.CateId=e.cateId),e.tags&&(o.Tags=e.tags),e.templateGroupId&&(o.TemplateGroupId=e.templateGroupId),e.storageLocation&&(o.StorageLocation=e.storageLocation),e.coverUrl&&(o.CoverURL=e.coverUrl),e.transCodeMode&&(o.TransCodeMode=e.transCodeMode),e.userData&&(o.UserData=JSON.stringify(e.userData)),e.WorkflowId&&(o.WorkflowId=e.WorkflowId),e.AppId&&(o.AppId=e.AppId);var i=c.default.makeUTF8sort(o,"=","&")+"&Signature="+c.default.aliyunEncodeURI(c.default.makeChangeSiga(o,e.accessKeySecret)),a="https://vod."+e.region+".aliyuncs.com/?"+i;s.default.get(a,function(e){try{e=JSON.parse(e)}catch(e){if(n)return void n({Code:"GetUploadAuthFailed",Message:"获取uploadauth失败"})}t&&t(e)},function(e){if(n){var t={Code:"GetUploadAuthFailed",Message:"获取uploadauth失败"};try{t=JSON.parse(e)}catch(e){}n(t)}})}},{key:"getImageUploadAuth",value:function(e,t,n){var r=(c.default.randomUUID(),c.default.randomUUID()),o={AccessKeyId:e.accessKeyId,SecurityToken:e.securityToken,Action:"CreateUploadImage",ImageType:e.imageType?e.imageType:"default",Version:"2017-03-21",Format:"JSON",SignatureMethod:"HMAC-SHA1",SignatureVersion:"1.0",SignatureNonce:r,RequestId:e.requestId};e.title&&(o.Title=e.title),e.imageExt&&(o.ImageExt=e.imageExt),e.tags&&(o.Tags=e.tags),e.storageLocation&&(o.StorageLocation=e.storageLocation);var i=c.default.makeUTF8sort(o,"=","&")+"&Signature="+c.default.aliyunEncodeURI(c.default.makeChangeSiga(o,e.accessKeySecret)),a="https://vod."+e.region+".aliyuncs.com/?"+i;s.default.get(a,function(e){e=JSON.parse(e),t&&t(e)},function(e){if(n){var t=JSON.parse(e);n(t)}})}}]),e}();t.default=l},function(e,t,n){!function(r,o,i){e.exports=t=o(n(0),n(28),n(29))}(0,function(e){return e.HmacSHA1})},function(e,t,n){!function(r,o){e.exports=t=o(n(0))}(0,function(e){return function(){var t=e,n=t.lib,r=n.WordArray,o=n.Hasher,i=t.algo,a=[],s=i.SHA1=o.extend({_doReset:function(){this._hash=new r.init([1732584193,4023233417,2562383102,271733878,3285377520])},_doProcessBlock:function(e,t){for(var n=this._hash.words,r=n[0],o=n[1],i=n[2],s=n[3],u=n[4],c=0;c<80;c++){if(c<16)a[c]=0|e[t+c];else{var l=a[c-3]^a[c-8]^a[c-14]^a[c-16];a[c]=l<<1|l>>>31}var f=(r<<5|r>>>27)+u+a[c];f+=c<20?1518500249+(o&i|~o&s):c<40?1859775393+(o^i^s):c<60?(o&i|o&s|i&s)-1894007588:(o^i^s)-899497514,u=s,s=i,i=o<<30|o>>>2,o=r,r=f}n[0]=n[0]+r|0,n[1]=n[1]+o|0,n[2]=n[2]+i|0,n[3]=n[3]+s|0,n[4]=n[4]+u|0},_doFinalize:function(){var e=this._data,t=e.words,n=8*this._nDataBytes,r=8*e.sigBytes;return t[r>>>5]|=128<<24-r%32,t[14+(r+64>>>9<<4)]=Math.floor(n/4294967296),t[15+(r+64>>>9<<4)]=n,e.sigBytes=4*t.length,this._process(),this._hash},clone:function(){var e=o.clone.call(this);return e._hash=this._hash.clone(),e}});t.SHA1=o._createHelper(s),t.HmacSHA1=o._createHmacHelper(s)}(),e.SHA1})},function(e,t,n){!function(r,o){e.exports=t=o(n(0))}(0,function(e){!function(){var t=e,n=t.lib,r=n.Base,o=t.enc,i=o.Utf8,a=t.algo;a.HMAC=r.extend({init:function(e,t){e=this._hasher=new e.init,"string"==typeof t&&(t=i.parse(t));var n=e.blockSize,r=4*n;t.sigBytes>r&&(t=e.finalize(t)),t.clamp();for(var o=this._oKey=t.clone(),a=this._iKey=t.clone(),s=o.words,u=a.words,c=0;c<n;c++)s[c]^=1549556828,u[c]^=909522486;o.sigBytes=a.sigBytes=r,this.reset()},reset:function(){var e=this._hasher;e.reset(),e.update(this._iKey)},update:function(e){return this._hasher.update(e),this},finalize:function(e){var t=this._hasher,n=t.finalize(e);return t.reset(),t.finalize(this._oKey.clone().concat(n))}})}()})},function(e,t,n){!function(r,o){e.exports=t=o(n(0))}(0,function(e){return function(){function t(e,t,n){for(var r=[],i=0,a=0;a<t;a++)if(a%4){var s=n[e.charCodeAt(a-1)]<<a%4*2,u=n[e.charCodeAt(a)]>>>6-a%4*2;r[i>>>2]|=(s|u)<<24-i%4*8,i++}return o.create(r,i)}var n=e,r=n.lib,o=r.WordArray,i=n.enc;i.Base64={stringify:function(e){var t=e.words,n=e.sigBytes,r=this._map;e.clamp();for(var o=[],i=0;i<n;i+=3)for(var a=t[i>>>2]>>>24-i%4*8&255,s=t[i+1>>>2]>>>24-(i+1)%4*8&255,u=t[i+2>>>2]>>>24-(i+2)%4*8&255,c=a<<16|s<<8|u,l=0;l<4&&i+.75*l<n;l++)o.push(r.charAt(c>>>6*(3-l)&63));var f=r.charAt(64);if(f)for(;o.length%4;)o.push(f);return o.join("")},parse:function(e){var n=e.length,r=this._map,o=this._reverseMap;if(!o){o=this._reverseMap=[];for(var i=0;i<r.length;i++)o[r.charCodeAt(i)]=i}var a=r.charAt(64);if(a){var s=e.indexOf(a);-1!==s&&(n=s)}return t(e,n,o)},_map:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/="}}(),e.enc.Base64})},function(e,t,n){"use strict";function r(e){return e&&e.__esModule?e:{default:e}}function o(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}Object.defineProperty(t,"__esModule",{value:!0});var i=function(){function e(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(t,n,r){return n&&e(t.prototype,n),r&&e(t,r),t}}(),a=n(8),s=r(a),u=n(7),c=r(u),l=n(4),f=r(l),h=n(6),d=r(h),p=n(1),g=r(p),v=n(9),y=r(v),_=n(2),m=n(11),T=n(10),S=function(){function e(){o(this,e)}return i(e,null,[{key:"getAuthInfo",value:function(e,t,n){var r=e+"|f#Ylm&^1TppeRhLg|"+n;return t&&(r=e+"|"+t+"|f#Ylm&^1TppeRhLg|"+n),_(T.parse(r)).toString(m)}},{key:"upload",value:function(t,n,r){var o=g.default.ISODateString(new Date),i=Math.floor((new Date).valueOf()/1e3),a=f.default.getClientId();a=f.default.setClientId(a);var u=e.getAuthInfo(t.userId,a,i),l=y.default.randomUUID(),h={Source:"WebSDK",BusinessType:"UploadVideo",Action:"ReportUploadProgress",TerminalType:"H5",DeviceModel:c.default.browser.name+(c.default.browser.version||""),AppVersion:d.default.version,AuthTimestamp:i,Timestamp:o,AuthInfo:u,FileName:t.file.name,FileSize:t.file.size,FileCreateTime:t.file.lastModified,FileHash:t.fileHash,UploadId:t.checkpoint.checkpoint.uploadId,PartSize:t.checkpoint.checkpoint.partSize,DonePartsCount:t.checkpoint.checkpoint.doneParts.length,UploadPoint:JSON.stringify(t.checkpoint),UploadRatio:t.checkpoint.loaded,UserId:t.userId,VideoId:t.videoId,Version:"2017-03-21",Format:"JSON",SignatureMethod:"HMAC-SHA1",SignatureVersion:"1.0",SignatureNonce:l};a&&(h.ClientId=a);var p=y.default.makeUTF8sort(h,"=","&")+"&Signature="+y.default.aliyunEncodeURI(y.default.makeChangeSiga(h,t.accessKeySecret)),v="https://vod."+t.region+".aliyuncs.com/?"+p;s.default.get(v,function(e){n&&n()},function(e){e&&(r(e),console.log(e))})}},{key:"get",value:function(t,n,r){var o=g.default.ISODateString(new Date),i=Math.floor((new Date).valueOf()/1e3),a=f.default.getClientId(),u=e.getAuthInfo(t.userId,a,i),l=y.default.randomUUID(),h={Source:"WebSDK",BusinessType:"UploadVideo",Action:"GetUploadProgress",TerminalType:"H5",DeviceModel:c.default.browser.name+(c.default.browser.version||""),AppVersion:d.default.version,AuthTimestamp:i,Timestamp:o,AuthInfo:u,UserId:t.userId,UploadInfoList:JSON.stringify(t.uploadInfoList),Version:"2017-03-21",Format:"JSON",SignatureMethod:"HMAC-SHA1",SignatureVersion:"1.0",SignatureNonce:l};a&&(h.ClientId=a);var p=y.default.makeUTF8sort(h,"=","&")+"&Signature="+y.default.aliyunEncodeURI(y.default.makeChangeSiga(h,t.accessKeySecret)),v="https://vod."+t.region+".aliyuncs.com/?"+p;s.default.get(v,function(e){var t={},r=a;e=e?JSON.parse(e):{},e.UploadProgress&&e.UploadProgress.UploadProgressList&&e.UploadProgress.UploadProgressList.length>0&&(t=e.UploadProgress.UploadProgressList[0],r=t.ClientId),f.default.setClientId(r),n&&n(t)},function(e){e&&(r(e),console.log(e))})}}]),e}();t.default=S},function(e,t,n){"use strict";function r(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}Object.defineProperty(t,"__esModule",{value:!0});var o=function(){function e(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(t,n,r){return n&&e(t.prototype,n),r&&e(t,r),t}}(),i=n(2),a=n(33),s=(n(11),function(){function e(){r(this,e)}return o(e,null,[{key:"getMd5",value:function(e,t,n){var r=new FileReader;r.onload=function(e){try{var n=e&&e.target.result||r.content,o=i(a.parse(n)),s=o.toString();t(s)}catch(e){console.log(e)}},r.onerror=function(e){console.log(e),errorCallback(e)};var o=File.prototype.slice||File.prototype.mozSlice||File.prototype.webkitSlice,s=o.call(e,0,1024);r.readAsBinaryString(s)}}]),e}());t.default=s},function(e,t,n){!function(r,o){e.exports=t=o(n(0))}(0,function(e){return e.enc.Latin1})}]);